import { 
  <PERSON>, 
  Pause, 
  Ski<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Shuffle, 
  Repeat, 
  Volume2,
  MoreHorizontal,
  ChevronDown
} from "lucide-react";
import { PlayerButton } from "@/components/ui/player-button";
import { Slider } from "@/components/ui/slider";
import { Card } from "@/components/ui/card";
import defaultAlbum from "@/assets/default-album.jpg";
import { useState } from "react";

interface NowPlayingProps {
  onClose: () => void;
}

export const NowPlaying = ({ onClose }: NowPlayingProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isShuffled, setIsShuffled] = useState(false);
  const [repeatMode, setRepeatMode] = useState(0); // 0: off, 1: all, 2: one
  const [progress, setProgress] = useState([35]);
  const [volume, setVolume] = useState([75]);
  const [isRandomMode, setIsRandomMode] = useState(false);

  const toggleRepeat = () => {
    setRepeatMode((prev) => (prev + 1) % 3);
  };

  return (
    <div className="fixed inset-0 z-50 bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border/50">
        <PlayerButton variant="ghost" size="sm" onClick={onClose}>
          <ChevronDown className="h-5 w-5" />
        </PlayerButton>
        <div className="text-center">
          <p className="text-sm font-medium text-foreground">Now Playing</p>
          <p className="text-xs text-muted-foreground">From "Favorites"</p>
        </div>
        <PlayerButton variant="ghost" size="sm">
          <MoreHorizontal className="h-5 w-5" />
        </PlayerButton>
      </div>

      <div className="px-6 py-8 flex flex-col h-full">
        {/* Album Art */}
        <div className="flex-1 flex items-center justify-center mb-8">
          <Card className="w-80 h-80 max-w-[80vw] max-h-[80vw] rounded-3xl overflow-hidden player-glow animate-float">
            <img 
              src={defaultAlbum} 
              alt="Current track" 
              className="w-full h-full object-cover"
            />
          </Card>
        </div>

        {/* Song Info */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            Neon Dreams
          </h1>
          <p className="text-lg text-muted-foreground mb-1">
            Electric Pulse
          </p>
          <p className="text-sm text-muted-foreground">
            Album: Synthwave Collection
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <Slider
            value={progress}
            onValueChange={setProgress}
            max={100}
            step={1}
            className="w-full mb-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>1:18</span>
            <span>3:42</span>
          </div>
        </div>

        {/* Main Controls */}
        <div className="flex items-center justify-center gap-6 mb-8">
          <PlayerButton
            variant="control"
            size="default"
            onClick={() => {
              setIsShuffled(!isShuffled);
              setIsRandomMode(!isRandomMode);
            }}
            className={isShuffled || isRandomMode ? "text-primary" : ""}
          >
            <Shuffle className="h-5 w-5" />
          </PlayerButton>

          <PlayerButton variant="control" size="default">
            <SkipBack className="h-6 w-6" />
          </PlayerButton>

          <PlayerButton
            variant="play"
            size="xl"
            onClick={() => setIsPlaying(!isPlaying)}
            className="animate-pulse-glow"
          >
            {isPlaying ? (
              <Pause className="h-8 w-8" />
            ) : (
              <Play className="h-8 w-8 ml-1" />
            )}
          </PlayerButton>

          <PlayerButton variant="control" size="default">
            <SkipForward className="h-6 w-6" />
          </PlayerButton>

          <PlayerButton
            variant="control"
            size="default"
            onClick={toggleRepeat}
            className={repeatMode > 0 ? "text-primary" : ""}
          >
            <Repeat className="h-5 w-5" />
            {repeatMode === 2 && (
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full text-[8px] flex items-center justify-center text-primary-foreground">
                1
              </span>
            )}
          </PlayerButton>
        </div>

        {/* Secondary Controls */}
        <div className="flex items-center justify-between">
          <PlayerButton
            variant="favorite"
            size="default"
            onClick={() => setIsFavorite(!isFavorite)}
            className={isFavorite ? "text-accent" : ""}
          >
            <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
          </PlayerButton>

          <div className="flex items-center gap-3 flex-1 max-w-32 mx-4">
            <Volume2 className="h-4 w-4 text-muted-foreground" />
            <Slider
              value={volume}
              onValueChange={setVolume}
              max={100}
              step={1}
              className="flex-1"
            />
          </div>

          <PlayerButton variant="control" size="default">
            <MoreHorizontal className="h-5 w-5" />
          </PlayerButton>
        </div>
      </div>
    </div>
  );
};