import { useState } from "react";
import { Clock, Volume2, Settings as SettingsIcon, Zap, Music } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";

export const Settings = () => {
  const [sleepTimer, setSleepTimer] = useState(0);
  const [bassBoost, setBassBoost] = useState([50]);
  const [treble, setTreble] = useState([50]);
  const [soundEnhancer, setSoundEnhancer] = useState(false);
  const [audioQuality, setAudioQuality] = useState("high");

  const startSleepTimer = (minutes: number) => {
    setSleepTimer(minutes);
    toast.success(`Sleep timer set for ${minutes} minutes`);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-foreground mb-2">Settings</h1>
        <p className="text-muted-foreground">Customize your music experience</p>
      </div>

      {/* Sleep Timer */}
      <Card className="p-6 glass-card">
        <div className="flex items-center gap-3 mb-4">
          <Clock className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Sleep Timer</h3>
        </div>
        <div className="grid grid-cols-2 gap-3 mb-4">
          <Button variant="outline" onClick={() => startSleepTimer(15)} className="h-12">
            15 min
          </Button>
          <Button variant="outline" onClick={() => startSleepTimer(30)} className="h-12">
            30 min
          </Button>
          <Button variant="outline" onClick={() => startSleepTimer(60)} className="h-12">
            1 hour
          </Button>
          <Button variant="outline" onClick={() => startSleepTimer(120)} className="h-12">
            2 hours
          </Button>
        </div>
        {sleepTimer > 0 && (
          <div className="text-center p-3 bg-primary/10 rounded-lg">
            <p className="text-sm text-primary font-medium">Timer active: {sleepTimer} minutes remaining</p>
          </div>
        )}
      </Card>

      {/* Sound Enhancer */}
      <Card className="p-6 glass-card">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Zap className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold text-foreground">Sound Enhancer</h3>
          </div>
          <Switch checked={soundEnhancer} onCheckedChange={setSoundEnhancer} />
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">Bass Boost</label>
            <Slider
              value={bassBoost}
              onValueChange={setBassBoost}
              max={100}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Low</span>
              <span>High</span>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">Treble</label>
            <Slider
              value={treble}
              onValueChange={setTreble}
              max={100}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Low</span>
              <span>High</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Audio Quality */}
      <Card className="p-6 glass-card">
        <div className="flex items-center gap-3 mb-4">
          <Volume2 className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Audio Quality</h3>
        </div>
        <Select value={audioQuality} onValueChange={setAudioQuality}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select quality" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low (128 kbps)</SelectItem>
            <SelectItem value="medium">Medium (256 kbps)</SelectItem>
            <SelectItem value="high">High (320 kbps)</SelectItem>
            <SelectItem value="lossless">Lossless (FLAC)</SelectItem>
          </SelectContent>
        </Select>
      </Card>

      {/* Equalizer Presets */}
      <Card className="p-6 glass-card">
        <div className="flex items-center gap-3 mb-4">
          <Music className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Equalizer Presets</h3>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <Button variant="outline" className="h-12">Pop</Button>
          <Button variant="outline" className="h-12">Rock</Button>
          <Button variant="outline" className="h-12">Jazz</Button>
          <Button variant="outline" className="h-12">Classical</Button>
          <Button variant="outline" className="h-12">Electronic</Button>
          <Button variant="outline" className="h-12">Custom</Button>
        </div>
      </Card>
    </div>
  );
};