@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 240 10% 8%;
    --foreground: 0 0% 98%;

    --card: 240 8% 12%;
    --card-foreground: 0 0% 95%;

    --popover: 240 8% 12%;
    --popover-foreground: 0 0% 95%;

    --primary: 280 100% 70%;
    --primary-foreground: 0 0% 10%;
    --primary-glow: 285 85% 82%;

    --secondary: 240 6% 18%;
    --secondary-foreground: 0 0% 90%;

    --muted: 240 6% 16%;
    --muted-foreground: 240 5% 64%;

    --accent: 315 100% 65%;
    --accent-foreground: 0 0% 10%;
    --accent-glow: 320 85% 75%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 20%;
    --input: 240 6% 18%;
    --ring: 280 100% 70%;

    --player-bg: 240 8% 10%;
    --player-card: 240 6% 14%;
    --glass: 240 8% 12% / 0.8;

    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--primary-glow)), hsl(var(--accent-glow)));
    --gradient-bg: radial-gradient(ellipse at top, hsl(var(--primary) / 0.1), transparent 50%);
    
    --shadow-glow: 0 0 40px hsl(var(--primary) / 0.3);
    --shadow-accent: 0 0 20px hsl(var(--accent) / 0.2);
    
    --radius: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    background: var(--gradient-bg);
    min-height: 100vh;
  }
  
  .gradient-text {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .glass-card {
    background: hsl(var(--glass));
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--border));
  }
  
  .player-glow {
    box-shadow: var(--shadow-glow);
  }
  
  .accent-glow {
    box-shadow: var(--shadow-accent);
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  
  @keyframes pulse-glow {
    0%, 100% { 
      box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
    }
    50% { 
      box-shadow: 0 0 40px hsl(var(--primary) / 0.6);
    }
  }
}