import { useState } from "react";
import { Header } from "@/components/Header";
import { Navigation } from "@/components/Navigation";
import { MiniPlayer } from "@/components/MiniPlayer";
import { HomeScreen } from "@/components/HomeScreen";
import { NowPlaying } from "@/components/NowPlaying";
import { Settings } from "@/components/Settings";
import { Developer } from "@/components/Developer";

const Index = () => {
  const [activeTab, setActiveTab] = useState("home");
  const [showNowPlaying, setShowNowPlaying] = useState(false);

  const renderContent = () => {
    switch (activeTab) {
      case "home":
        return <HomeScreen />;
      case "search":
        return (
          <div className="p-6 text-center">
            <h2 className="text-xl font-semibold text-foreground mb-2">Search</h2>
            <p className="text-muted-foreground">Search functionality coming soon!</p>
          </div>
        );
      case "library":
        return (
          <div className="p-6 text-center">
            <h2 className="text-xl font-semibold text-foreground mb-2">Library</h2>
            <p className="text-muted-foreground">Your music library will appear here!</p>
          </div>
        );
      case "settings":
        return <Settings />;
      case "developer":
        return <Developer />;
      default:
        return <HomeScreen />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-2">
        {renderContent()}
      </main>

      <div onClick={() => setShowNowPlaying(true)}>
        <MiniPlayer />
      </div>
      
      <Navigation activeTab={activeTab} onTabChange={setActiveTab} />

      {showNowPlaying && (
        <NowPlaying onClose={() => setShowNowPlaying(false)} />
      )}
    </div>
  );
};

export default Index;
