import { <PERSON>, Heart, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/player-button";
import { Card } from "@/components/ui/card";
import defaultAlbum from "@/assets/default-album.jpg";
import heroBackground from "@/assets/hero-background.jpg";

const recentTracks = [
  { id: 1, title: "Neon Dreams", artist: "Electric Pulse", duration: "3:42", plays: 1247 },
  { id: 2, title: "Midnight Journey", artist: "Cosmic Waves", duration: "4:15", plays: 892 },
  { id: 3, title: "Digital Hearts", artist: "Synth Valley", duration: "3:28", plays: 1456 },
  { id: 4, title: "Future Bass", artist: "Nova Sound", duration: "3:55", plays: 743 },
];

const topTracks = [
  { id: 1, title: "Stellar Rhythm", artist: "Galaxy Beats", duration: "4:02", plays: 2847 },
  { id: 2, title: "Quantum Melody", artist: "Space Echo", duration: "3:33", plays: 2156 },
  { id: 3, title: "Aurora Vibes", artist: "Northern Lights", duration: "4:28", plays: 1923 },
];

export const HomeScreen = () => {
  return (
    <div className="pb-32 px-4">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-2xl mb-6">
        <div className="absolute inset-0">
          <img 
            src={heroBackground} 
            alt="Music visualization" 
            className="w-full h-full object-cover opacity-30"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
        </div>
        <div className="relative p-6 text-center">
          <h2 className="text-3xl font-bold gradient-text mb-2 animate-float">
            Welcome to Unique Player
          </h2>
          <p className="text-muted-foreground mb-6">
            Discover your perfect sound experience
          </p>
          <PlayerButton variant="play" size="lg" className="animate-pulse-glow">
            <Play className="h-6 w-6 ml-1" />
          </PlayerButton>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <Card className="glass-card p-4 text-center">
          <TrendingUp className="h-5 w-5 text-primary mx-auto mb-2" />
          <p className="text-sm font-semibold text-foreground">1,247</p>
          <p className="text-xs text-muted-foreground">Total Plays</p>
        </Card>
        <Card className="glass-card p-4 text-center">
          <Clock className="h-5 w-5 text-accent mx-auto mb-2" />
          <p className="text-sm font-semibold text-foreground">24h 32m</p>
          <p className="text-xs text-muted-foreground">Listening Time</p>
        </Card>
        <Card className="glass-card p-4 text-center">
          <Star className="h-5 w-5 text-yellow-500 mx-auto mb-2" />
          <p className="text-sm font-semibold text-foreground">147</p>
          <p className="text-xs text-muted-foreground">Favorites</p>
        </Card>
      </div>

      {/* Recently Played */}
      <section className="mb-6">
        <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Recently Played
        </h3>
        <div className="space-y-3">
          {recentTracks.map((track) => (
            <Card key={track.id} className="glass-card p-3">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-player-card flex-shrink-0">
                  <img 
                    src={defaultAlbum} 
                    alt={track.title} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-foreground truncate">{track.title}</h4>
                  <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                  <p className="text-xs text-muted-foreground">{track.plays} plays</p>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">{track.duration}</span>
                  <PlayerButton variant="ghost" size="sm">
                    <Play className="h-3 w-3" />
                  </PlayerButton>
                  <PlayerButton variant="ghost" size="sm">
                    <MoreHorizontal className="h-3 w-3" />
                  </PlayerButton>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>

      {/* Top Tracks */}
      <section>
        <h3 className="text-lg font-semibold text-foreground mb-3 flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-accent" />
          Most Played
        </h3>
        <div className="space-y-3">
          {topTracks.map((track, index) => (
            <Card key={track.id} className="glass-card p-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center text-primary-foreground font-bold text-sm">
                  {index + 1}
                </div>
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-player-card flex-shrink-0">
                  <img 
                    src={defaultAlbum} 
                    alt={track.title} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-foreground truncate">{track.title}</h4>
                  <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                  <p className="text-xs text-muted-foreground">{track.plays} plays</p>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">{track.duration}</span>
                  <PlayerButton variant="favorite" size="sm">
                    <Heart className="h-3 w-3" />
                  </PlayerButton>
                  <PlayerButton variant="ghost" size="sm">
                    <Play className="h-3 w-3" />
                  </PlayerButton>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
};