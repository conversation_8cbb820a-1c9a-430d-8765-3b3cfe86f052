import { Github, Mail, Globe, Heart, Code, Zap } from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export const Developer = () => {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center">
          <Code className="h-12 w-12 text-white" />
        </div>
        <h1 className="text-2xl font-bold text-foreground mb-2">Developer</h1>
        <p className="text-muted-foreground">Meet the creator behind Unique Player</p>
      </div>

      {/* Developer Info */}
      <Card className="p-6 glass-card text-center">
        <h2 className="text-xl font-bold text-foreground mb-2">Unique Shiwakoti</h2>
        <p className="text-primary font-semibold mb-4">Codovo Studio</p>
        <p className="text-muted-foreground text-sm leading-relaxed mb-6">
          Passionate mobile app developer dedicated to creating exceptional music experiences. 
          Combining innovative design with powerful functionality to bring you the ultimate music player.
        </p>
        
        <div className="flex justify-center gap-4 mb-6">
          <Button variant="outline" size="sm" className="h-10 px-4">
            <Github className="h-4 w-4 mr-2" />
            GitHub
          </Button>
          <Button variant="outline" size="sm" className="h-10 px-4">
            <Mail className="h-4 w-4 mr-2" />
            Contact
          </Button>
          <Button variant="outline" size="sm" className="h-10 px-4">
            <Globe className="h-4 w-4 mr-2" />
            Website
          </Button>
        </div>
      </Card>

      {/* Studio Info */}
      <Card className="p-6 glass-card">
        <div className="flex items-center gap-3 mb-4">
          <Zap className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold text-foreground">Codovo Studio</h3>
        </div>
        <p className="text-muted-foreground text-sm leading-relaxed mb-4">
          A creative technology studio focused on building innovative mobile applications 
          that enhance daily life through intuitive design and cutting-edge technology.
        </p>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            <span className="text-sm text-foreground">Mobile App Development</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            <span className="text-sm text-foreground">UI/UX Design</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            <span className="text-sm text-foreground">Audio Technology</span>
          </div>
        </div>
      </Card>

      {/* App Stats */}
      <Card className="p-6 glass-card">
        <h3 className="text-lg font-semibold text-foreground mb-4">Unique Player Stats</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">1.0.0</div>
            <div className="text-xs text-muted-foreground">Version</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">2024</div>
            <div className="text-xs text-muted-foreground">Release Year</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">React</div>
            <div className="text-xs text-muted-foreground">Framework</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">Native</div>
            <div className="text-xs text-muted-foreground">Platform</div>
          </div>
        </div>
      </Card>

      {/* Thank You */}
      <Card className="p-6 glass-card text-center">
        <Heart className="h-8 w-8 text-accent mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-foreground mb-2">Thank You!</h3>
        <p className="text-muted-foreground text-sm">
          Thank you for using Unique Player. Your support drives us to create better experiences.
        </p>
      </Card>
    </div>
  );
};