import { <PERSON>, Setting<PERSON> } from "lucide-react";
import { PlayerButton } from "@/components/ui/player-button";
import logo from "@/assets/unique-player-logo.jpg";

export const Header = () => {
  return (
    <header className="sticky top-0 z-30 glass-card border-b border-border/50 backdrop-blur-xl">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <img 
            src={logo} 
            alt="Unique Player" 
            className="w-8 h-8 rounded-lg"
          />
          <div>
            <h1 className="text-lg font-bold gradient-text">Unique Player</h1>
            <p className="text-xs text-muted-foreground">Feel the Music</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <PlayerButton variant="ghost" size="sm">
            <Bell className="h-4 w-4" />
          </PlayerButton>
          <PlayerButton variant="ghost" size="sm">
            <Settings className="h-4 w-4" />
          </PlayerButton>
        </div>
      </div>
    </header>
  );
};