import { <PERSON>, Pause, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { PlayerButton } from "@/components/ui/player-button";
import defaultAlbum from "@/assets/default-album.jpg";
import { useState } from "react";

export const MiniPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 glass-card border-t p-3">
      <div className="flex items-center gap-3">
        {/* Album Art & Song Info */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="w-12 h-12 rounded-lg overflow-hidden bg-player-card">
            <img 
              src={defaultAlbum} 
              alt="Current track" 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold text-foreground truncate">
              Sample Song
            </h4>
            <p className="text-xs text-muted-foreground truncate">
              Sample Artist
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2">
          <PlayerButton
            variant="ghost"
            size="sm"
            onClick={() => setIsFavorite(!isFavorite)}
          >
            <Heart 
              className={`h-4 w-4 ${isFavorite ? 'fill-accent text-accent' : ''}`} 
            />
          </PlayerButton>
          
          <PlayerButton
            variant="mini"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4 ml-0.5" />
            )}
          </PlayerButton>
          
          <PlayerButton variant="ghost" size="sm">
            <SkipForward className="h-4 w-4" />
          </PlayerButton>
        </div>
      </div>
    </div>
  );
};