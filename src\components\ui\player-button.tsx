import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const playerButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        play: "bg-gradient-to-r from-primary to-accent text-primary-foreground hover:shadow-lg hover:scale-105 active:scale-95 player-glow",
        control: "bg-player-card border border-border text-foreground hover:bg-secondary hover:scale-105 active:scale-95",
        mini: "bg-player-card/50 backdrop-blur-sm border border-border/50 text-foreground hover:bg-secondary/80 hover:scale-105",
        ghost: "text-muted-foreground hover:text-foreground hover:bg-secondary/50 hover:scale-105",
        favorite: "text-accent hover:text-accent-foreground hover:bg-accent/20 hover:scale-105",
      },
      size: {
        sm: "h-8 w-8",
        default: "h-12 w-12",
        lg: "h-16 w-16",
        xl: "h-20 w-20",
      },
    },
    defaultVariants: {
      variant: "control",
      size: "default",
    },
  }
)

export interface PlayerButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof playerButtonVariants> {
  asChild?: boolean
}

const PlayerButton = React.forwardRef<HTMLButtonElement, PlayerButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(playerButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
PlayerButton.displayName = "PlayerButton"

export { PlayerButton, playerButtonVariants }